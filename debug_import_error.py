#!/usr/bin/env python3
"""
Debug script per testare il riconoscimento degli errori di import
"""

import sys
from pathlib import Path

# Aggiungi path NEUROGLYPH
sys.path.insert(0, str(Path(__file__).parent))

def test_import_error_detection():
    """Testa il riconoscimento degli errori di import."""
    
    # Codice con errore di import
    test_code = """import nonexistent_module_xyz
result = nonexistent_module_xyz.some_function()
print("Hello world")
"""
    
    print("🔍 Testing import error detection...")
    print(f"Test code:\n{test_code}")
    
    # Test 1: Compilazione
    try:
        compile(test_code, '<string>', 'exec')
        print("✅ Compilation successful (expected)")
    except Exception as e:
        print(f"❌ Compilation failed: {e}")
    
    # Test 2: Esecuzione
    try:
        restricted_builtins = {
            "__import__": __import__,
            "__builtins__": {}
        }
        exec(test_code, restricted_builtins, {})
        print("❌ Execution successful (unexpected)")
    except Exception as e:
        print(f"✅ Execution failed: {type(e).__name__}: {e}")
        error_message = str(e)
        
        # Test pattern matching
        from neuroglyph.cognitive.patch_generator import PatchGenerator
        generator = PatchGenerator()
        
        is_import_error = generator._is_import_error(error_message)
        print(f"Is import error: {is_import_error}")
        
        if is_import_error:
            module_name = generator._extract_module_name(error_message)
            print(f"Extracted module name: {module_name}")
        
        # Test ErrorAnalyzer
        from neuroglyph.cognitive.error_analyzer import ErrorAnalyzer
        from neuroglyph.cognitive.validation_structures import ValidationResult, ValidationError, ValidationLevel, ValidationSeverity, ValidationErrorType
        
        analyzer = ErrorAnalyzer()
        
        # Crea ValidationResult simulato
        result = ValidationResult()
        val_error = ValidationError(
            error_type=ValidationErrorType.MISSING_PREMISES,
            severity=ValidationSeverity.ERROR,
            level=ValidationLevel.SEMANTIC,
            message=error_message,
            line_number=1
        )
        val_error.context = {'code': test_code}
        result.add_error(val_error)
        result.finalize()
        
        # Analizza errore
        error_analyses = analyzer.analyze_validation_error(result)
        print(f"Error analyses generated: {len(error_analyses)}")
        
        for i, analysis in enumerate(error_analyses):
            print(f"Analysis {i+1}:")
            print(f"  Error type: {analysis.error_type}")
            print(f"  Error message: {analysis.error_message}")
            print(f"  Error pattern: {analysis.error_pattern}")
            
            # Test patch generation
            patches = generator.generate_patches(analysis)
            print(f"  Patches generated: {len(patches)}")
            
            for j, patch in enumerate(patches):
                print(f"    Patch {j+1}: {patch.patch_strategy} (confidence: {patch.confidence})")

if __name__ == "__main__":
    test_import_error_detection()
