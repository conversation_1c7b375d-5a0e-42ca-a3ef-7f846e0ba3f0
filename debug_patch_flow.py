#!/usr/bin/env python3
"""
Debug script per tracciare il flusso completo del patch engine
"""

import sys
from pathlib import Path

# Aggiungi path NEUROGLYPH
sys.path.insert(0, str(Path(__file__).parent))

def test_patch_flow():
    """Testa il flusso completo del patch engine."""
    
    from neuroglyph.cognitive.adaptive_patcher import NGAdaptivePatcher
    from neuroglyph.cognitive.validation_structures import ValidationResult, ValidationError, ValidationLevel, ValidationSeverity, ValidationErrorType
    
    print("🔍 Testing complete patch flow...")
    
    # Inizializza patcher
    patcher = NGAdaptivePatcher()
    
    # Codice con errore di import
    test_code = """import nonexistent_module_xyz
result = nonexistent_module_xyz.some_function()
print("Hello world")
"""
    
    print(f"Test code:\n{test_code}")
    
    # Crea ValidationResult simulato
    result = ValidationResult()
    val_error = ValidationError(
        error_type=ValidationErrorType.MISSING_PREMISES,
        severity=ValidationSeverity.ERROR,
        level=ValidationLevel.SEMANTIC,
        message="ModuleNotFoundError: No module named 'nonexistent_module_xyz'",
        line_number=1
    )
    val_error.context = {'code': test_code}
    result.add_error(val_error)
    result.finalize()
    
    print(f"ValidationResult created with {len(result.semantic_errors)} semantic errors")
    
    # Applica patch
    print("\n🔧 Applying patches...")
    patch_results = patcher.patch_validation_errors(result)
    
    print(f"Patch results: {len(patch_results)}")
    
    for i, patch_result in enumerate(patch_results):
        print(f"\nPatch {i+1}:")
        print(f"  Success: {patch_result.success}")
        print(f"  Status: {patch_result.status}")
        print(f"  Validation passed: {patch_result.validation_passed}")
        print(f"  Error message: {patch_result.error_message}")
        print(f"  Output: {patch_result.output[:100] if patch_result.output else 'None'}...")
        
        if hasattr(patch_result, 'metadata') and patch_result.metadata:
            print(f"  Metadata: {patch_result.metadata}")

if __name__ == "__main__":
    test_patch_flow()
