"""
NEUROGLYPH Error Analyzer
Analizzatore di errori per adaptive patcher
"""

import re
import ast
import time
import traceback
from typing import List, Dict, Any, Optional, Tuple

from .patcher_structures import (
    ErrorAnalysis, ErrorType, PatcherConfig
)
# Import lazy per evitare cicli
# from .validation_structures import ValidationResult
# from .sandbox_structures import ExecutionResult
# from .reasoning_structures import ReasoningGraph


class ErrorAnalyzer:
    """
    Analizzatore di errori per NEUROGLYPH.
    Analizza errori da diverse fonti e identifica pattern.
    """
    
    def __init__(self, config: Optional[PatcherConfig] = None):
        """
        Inizializza l'analizzatore di errori.
        
        Args:
            config: Configurazione patcher
        """
        self.config = config or PatcherConfig()
        self.error_patterns = self._load_error_patterns()
        self.error_history = []
        
        print("🔍 ErrorAnalyzer inizializzato")
        print(f"   - Error patterns: {len(self.error_patterns)}")
        print(f"   - Learning mode: {self.config.learning_mode}")
    
    def analyze_execution_error(self, execution_result) -> Optional[ErrorAnalysis]:
        """
        Analizza errore da ExecutionResult.
        
        Args:
            execution_result: Risultato esecuzione con errore
            
        Returns:
            Analisi dell'errore o None se nessun errore
        """
        if execution_result.success:
            return None
        
        analysis = ErrorAnalysis()
        
        # Determina tipo errore
        analysis.error_type = self._classify_execution_error(execution_result.error_type)
        analysis.error_message = execution_result.error_message
        analysis.traceback = execution_result.traceback
        
        # Estrai stack trace
        if execution_result.traceback:
            analysis.stack_trace = execution_result.traceback.split('\n')
        
        # Analizza contesto
        analysis.execution_context = {
            'status': execution_result.status.value,
            'duration': execution_result.duration,
            'memory_usage': execution_result.resource_usage.peak_memory_mb,
            'stdout': execution_result.stdout[:500],  # Primi 500 char
            'stderr': execution_result.stderr[:500]
        }
        
        # Determina severità
        analysis.severity = self._determine_severity(execution_result)
        analysis.impact_score = self._calculate_impact_score(execution_result)
        
        # Identifica pattern
        analysis.error_pattern = self._identify_error_pattern(analysis)
        analysis.root_cause = self._identify_root_cause(analysis)
        analysis.contributing_factors = self._identify_contributing_factors(analysis)
        
        # Localizzazione errore
        analysis.error_location = self._extract_error_location(execution_result)
        analysis.error_context = self._extract_error_context(execution_result)
        
        self.error_history.append(analysis)
        
        return analysis
    
    def analyze_validation_error(self, validation_result) -> List[ErrorAnalysis]:
        """
        Analizza errori da ValidationResult.
        
        Args:
            validation_result: Risultato validazione
            
        Returns:
            Lista di analisi errori
        """
        analyses = []

        # Analizza tutti gli errori (non solo critici) - PATCH 2.2
        all_errors = (validation_result.syntax_errors +
                     validation_result.semantic_errors +
                     validation_result.logic_errors)

        for error in all_errors:
            analysis = ErrorAnalysis()
            # Determina il tipo di errore più specifico basato sul messaggio
            analysis.error_type = self._determine_error_type_from_message(error.message)
            analysis.error_message = error.message
            analysis.error_location = error.node_id or error.step_id or ""
            analysis.severity = error.severity.value  # Usa severità reale

            # Impact score basato su severità
            severity_impact = {
                "critical": 0.9,
                "error": 0.7,
                "warning": 0.4,
                "info": 0.2
            }
            analysis.impact_score = severity_impact.get(error.severity.value, 0.5)

            # Contesto validazione
            analysis.execution_context = {
                'validation_level': error.level.value,
                'error_type': error.error_type.value,
                'context': error.context
            }

            # Usa pattern identification più intelligente
            analysis.error_pattern = self._identify_error_pattern(analysis)
            analysis.root_cause = self._identify_root_cause(analysis)

            analyses.append(analysis)

        return analyses
    
    def analyze_reasoning_error(self, reasoning_graph) -> List[ErrorAnalysis]:
        """
        Analizza errori nel reasoning graph.
        
        Args:
            reasoning_graph: Grafo di reasoning
            
        Returns:
            Lista di analisi errori
        """
        analyses = []
        
        # Analizza contraddizioni
        for fact1_id, fact2_id in reasoning_graph.contradictions:
            analysis = ErrorAnalysis()
            analysis.error_type = ErrorType.LOGIC_ERROR
            analysis.error_message = f"Contradiction between facts: {fact1_id}, {fact2_id}"
            analysis.error_location = f"facts:{fact1_id},{fact2_id}"
            analysis.severity = "high"
            analysis.impact_score = 0.8
            
            analysis.execution_context = {
                'contradiction_count': len(reasoning_graph.contradictions),
                'total_facts': reasoning_graph.total_facts,
                'total_steps': reasoning_graph.total_steps
            }
            
            analysis.error_pattern = "logical_contradiction"
            analysis.root_cause = "Conflicting facts in reasoning"
            
            analyses.append(analysis)
        
        # Analizza percorsi con score basso
        for path in reasoning_graph.paths:
            if path.overall_score < 0.3:  # Score molto basso
                analysis = ErrorAnalysis()
                analysis.error_type = ErrorType.REASONING_ERROR
                analysis.error_message = f"Low quality reasoning path: score {path.overall_score:.3f}"
                analysis.error_location = f"path:{path.path_id}"
                analysis.severity = "medium"
                analysis.impact_score = 0.6
                
                analysis.execution_context = {
                    'path_score': path.overall_score,
                    'path_depth': path.depth,
                    'has_contradiction': path.has_contradiction
                }
                
                analysis.error_pattern = "low_quality_reasoning"
                analysis.root_cause = "Poor reasoning chain quality"
                
                analyses.append(analysis)
        
        return analyses
    
    def _classify_execution_error(self, error_type: str) -> ErrorType:
        """Classifica tipo di errore da esecuzione."""
        error_mapping = {
            'SyntaxError': ErrorType.SYNTAX_ERROR,
            'NameError': ErrorType.RUNTIME_ERROR,
            'TypeError': ErrorType.RUNTIME_ERROR,
            'ValueError': ErrorType.RUNTIME_ERROR,
            'AttributeError': ErrorType.RUNTIME_ERROR,
            'ImportError': ErrorType.RUNTIME_ERROR,
            'TimeoutException': ErrorType.PERFORMANCE_ERROR,
            'SecurityViolation': ErrorType.SECURITY_ERROR,
        }
        
        return error_mapping.get(error_type, ErrorType.RUNTIME_ERROR)
    
    def _determine_severity(self, execution_result) -> str:
        """Determina severità dell'errore."""
        if execution_result.error_type in ['SecurityViolation', 'TimeoutException']:
            return "critical"
        elif execution_result.error_type in ['SyntaxError', 'ImportError']:
            return "high"
        elif execution_result.error_type in ['TypeError', 'ValueError']:
            return "medium"
        else:
            return "low"
    
    def _calculate_impact_score(self, execution_result) -> float:
        """Calcola score di impatto dell'errore."""
        base_score = 0.5
        
        # Aumenta per errori critici
        if execution_result.error_type in ['SecurityViolation']:
            base_score += 0.4
        elif execution_result.error_type in ['TimeoutException']:
            base_score += 0.3
        elif execution_result.error_type in ['SyntaxError']:
            base_score += 0.2
        
        # Aumenta per durata lunga (indica problema performance)
        if execution_result.duration > 5.0:
            base_score += 0.1
        
        # Aumenta per uso memoria alto
        if execution_result.resource_usage.peak_memory_mb > 100:
            base_score += 0.1
        
        return min(1.0, base_score)
    
    def _identify_error_pattern(self, analysis: ErrorAnalysis) -> str:
        """Identifica pattern dell'errore."""
        message = analysis.error_message.lower()
        
        # Pattern comuni
        for pattern_name, pattern_regex in self.error_patterns.items():
            if re.search(pattern_regex, message):
                return pattern_name
        
        # Pattern basato su tipo errore
        if analysis.error_type == ErrorType.SYNTAX_ERROR:
            if "invalid syntax" in message:
                return "syntax_invalid"
            elif "unexpected" in message:
                return "syntax_unexpected"
        elif analysis.error_type == ErrorType.RUNTIME_ERROR:
            if "not defined" in message:
                return "undefined_variable"
            elif "has no attribute" in message:
                return "missing_attribute"
            elif "not callable" in message:
                return "not_callable"
        
        return "unknown_pattern"
    
    def _identify_root_cause(self, analysis: ErrorAnalysis) -> str:
        """Identifica causa principale dell'errore."""
        pattern = analysis.error_pattern
        error_type = analysis.error_type
        
        # Mapping pattern -> root cause
        root_cause_mapping = {
            "undefined_variable": "Variable not declared or out of scope",
            "missing_attribute": "Object missing expected attribute or method",
            "not_callable": "Attempting to call non-function object",
            "syntax_invalid": "Invalid Python syntax",
            "syntax_unexpected": "Unexpected token or structure",
            "logical_contradiction": "Conflicting logical statements",
            "low_quality_reasoning": "Insufficient or poor reasoning chain"
        }
        
        return root_cause_mapping.get(pattern, f"Unknown cause for {error_type.value}")
    
    def _identify_contributing_factors(self, analysis: ErrorAnalysis) -> List[str]:
        """Identifica fattori contribuenti."""
        factors = []
        
        # Fattori basati su contesto
        context = analysis.execution_context
        
        if context.get('duration', 0) > 5.0:
            factors.append("Long execution time")
        
        if context.get('memory_usage', 0) > 100:
            factors.append("High memory usage")
        
        if context.get('stderr'):
            factors.append("Error output present")
        
        # Fattori basati su pattern
        if "import" in analysis.error_message.lower():
            factors.append("Module import issue")
        
        if "permission" in analysis.error_message.lower():
            factors.append("Permission or security issue")
        
        return factors
    
    def _extract_error_location(self, execution_result) -> str:
        """Estrae localizzazione dell'errore."""
        if execution_result.traceback:
            # Cerca pattern "line X" nel traceback
            line_match = re.search(r'line (\d+)', execution_result.traceback)
            if line_match:
                return f"line:{line_match.group(1)}"
        
        return "unknown_location"
    
    def _extract_error_context(self, execution_result) -> str:
        """Estrae contesto dell'errore."""
        context_parts = []
        
        if execution_result.execution_context:
            code = execution_result.execution_context.code
            if code:
                # Primi 100 caratteri del codice
                context_parts.append(f"code: {code[:100]}")
        
        if execution_result.stdout:
            context_parts.append(f"stdout: {execution_result.stdout[:50]}")
        
        if execution_result.stderr:
            context_parts.append(f"stderr: {execution_result.stderr[:50]}")
        
        return " | ".join(context_parts)
    
    def _identify_validation_pattern(self, error) -> str:
        """Identifica pattern da errore di validazione."""
        error_type = error.error_type.value
        
        pattern_mapping = {
            "ast_parse_error": "syntax_validation_error",
            "missing_premises": "logical_dependency_error",
            "invalid_references": "reference_validation_error",
            "circular_inference": "logical_circularity_error",
            "confidence_underflow": "confidence_validation_error"
        }
        
        return pattern_mapping.get(error_type, "validation_error")
    
    def _identify_validation_root_cause(self, error) -> str:
        """Identifica causa principale da errore di validazione."""
        error_type = error.error_type.value
        
        cause_mapping = {
            "ast_parse_error": "Invalid AST structure or syntax",
            "missing_premises": "Required logical premises not found",
            "invalid_references": "Invalid or missing references",
            "circular_inference": "Circular dependency in reasoning",
            "confidence_underflow": "Confidence below acceptable threshold"
        }
        
        return cause_mapping.get(error_type, "Unknown validation issue")

    def _determine_error_type_from_message(self, message: str) -> ErrorType:
        """Determina il tipo di errore più specifico dal messaggio."""
        message_lower = message.lower()

        # Errori di import (PRIORITÀ 2: Import Error Recognition)
        if ("no module named" in message_lower or
            "modulenotfounderror" in message_lower or
            "importerror" in message_lower or
            "cannot import name" in message_lower):
            return ErrorType.RUNTIME_ERROR  # Trattiamo import come runtime error

        # Errori di sintassi
        if "invalid syntax" in message_lower or "syntaxerror" in message_lower:
            return ErrorType.SYNTAX_ERROR

        # Errori di runtime
        if ("name" in message_lower and "not defined" in message_lower) or "nameerror" in message_lower:
            return ErrorType.RUNTIME_ERROR

        if "has no attribute" in message_lower or "attributeerror" in message_lower:
            return ErrorType.RUNTIME_ERROR

        if "not callable" in message_lower or "typeerror" in message_lower:
            return ErrorType.RUNTIME_ERROR

        if "no module named" in message_lower or "importerror" in message_lower or "modulenotfounderror" in message_lower:
            return ErrorType.RUNTIME_ERROR

        # Default: validation error
        return ErrorType.VALIDATION_ERROR

    def _load_error_patterns(self) -> Dict[str, str]:
        """Carica pattern di errori comuni."""
        return {
            # Runtime errors
            "undefined_variable": r"name '(\w+)' is not defined",
            "missing_attribute": r"'(\w+)' object has no attribute '(\w+)'",
            "not_callable": r"'(\w+)' object is not callable",
            "import_error": r"no module named '(\w+)'",
            "syntax_error": r"invalid syntax",
            "indentation_error": r"unexpected indent|unindent",
            "type_error": r"unsupported operand type|can't convert",
            "value_error": r"invalid literal|not enough values",
            "key_error": r"keyerror",
            "index_error": r"list index out of range",
            "timeout_error": r"timeout|time limit exceeded",
            "memory_error": r"memory|out of memory",
            "permission_error": r"permission denied|access denied",

            # Validation errors (PATCH 2.2: aggiunti per patch-testing)
            "missing_premises": r"missing.premises|premise.not.found",
            "invalid_references": r"invalid.reference|reference.error",
            "circular_inference": r"circular.inference|circular.dependency",
            "unhandled_contradiction": r"contradiction|conflicting.facts",
            "confidence_underflow": r"confidence.too.low|low.confidence"
        }
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """Ottiene statistiche degli errori."""
        if not self.error_history:
            return {}
        
        # Conta per tipo
        type_counts = {}
        pattern_counts = {}
        severity_counts = {}
        
        for analysis in self.error_history:
            # Tipo errore
            error_type = analysis.error_type.value
            type_counts[error_type] = type_counts.get(error_type, 0) + 1
            
            # Pattern
            pattern = analysis.error_pattern
            pattern_counts[pattern] = pattern_counts.get(pattern, 0) + 1
            
            # Severità
            severity = analysis.severity
            severity_counts[severity] = severity_counts.get(severity, 0) + 1
        
        return {
            'total_errors': len(self.error_history),
            'error_types': type_counts,
            'error_patterns': pattern_counts,
            'severity_distribution': severity_counts,
            'avg_impact_score': sum(a.impact_score for a in self.error_history) / len(self.error_history)
        }
