"""
NEUROGLYPH Patch Generator
Generatore di patch per auto-correzione
"""

import re
import ast
import time
from typing import List, Dict, Any, Optional, Tuple

from .patcher_structures import (
    ErrorAnalysis, PatchCandidate, PatchType, PatcherConfig,
    ErrorType, LearningPattern
)


class PatchGenerator:
    """
    Generatore di patch per NEUROGLYPH.
    Genera candidati patch basati su analisi errori e pattern appresi.
    """
    
    def __init__(self, config: Optional[PatcherConfig] = None):
        """
        Inizializza il generatore di patch.
        
        Args:
            config: Configurazione patcher
        """
        self.config = config or PatcherConfig()
        self.patch_strategies = self._load_patch_strategies()
        self.learned_patterns = []
        
        print("🔧 PatchGenerator inizializzato")
        print(f"   - Patch strategies: {len(self.patch_strategies)}")
        print(f"   - Max candidates: {self.config.max_patch_candidates}")
        print(f"   - Min confidence: {self.config.min_confidence_threshold}")
    
    def generate_patches(self, error_analysis: ErrorAnalysis) -> List[PatchCandidate]:
        """
        Genera candidati patch per un errore.
        
        Args:
            error_analysis: Analisi dell'errore
            
        Returns:
            Lista di candidati patch
        """
        candidates = []
        
        # Genera patch basate su pattern dell'errore
        pattern_patches = self._generate_pattern_based_patches(error_analysis)
        candidates.extend(pattern_patches)
        
        # Genera patch basate su tipo errore
        type_patches = self._generate_type_based_patches(error_analysis)
        candidates.extend(type_patches)
        
        # Genera patch basate su pattern appresi
        learned_patches = self._generate_learned_patches(error_analysis)
        candidates.extend(learned_patches)
        
        # Filtra e ordina candidati
        filtered_candidates = self._filter_candidates(candidates)
        sorted_candidates = self._sort_candidates(filtered_candidates)
        
        # Limita numero candidati
        return sorted_candidates[:self.config.max_patch_candidates]
    
    def _generate_pattern_based_patches(self, error_analysis: ErrorAnalysis) -> List[PatchCandidate]:
        """Genera patch basate su pattern dell'errore."""
        candidates = []
        pattern = error_analysis.error_pattern
        
        if pattern in self.patch_strategies:
            strategy = self.patch_strategies[pattern]
            
            for patch_template in strategy['patches']:
                candidate = self._create_patch_from_template(
                    error_analysis, patch_template, strategy
                )
                if candidate:
                    candidates.append(candidate)
        
        return candidates
    
    def _generate_type_based_patches(self, error_analysis: ErrorAnalysis) -> List[PatchCandidate]:
        """Genera patch basate su tipo errore."""
        candidates = []
        error_type = error_analysis.error_type
        
        if error_type == ErrorType.SYNTAX_ERROR:
            candidates.extend(self._generate_syntax_patches(error_analysis))
        elif error_type == ErrorType.RUNTIME_ERROR:
            candidates.extend(self._generate_runtime_patches(error_analysis))
        elif error_type == ErrorType.LOGIC_ERROR:
            candidates.extend(self._generate_logic_patches(error_analysis))
        elif error_type == ErrorType.PERFORMANCE_ERROR:
            candidates.extend(self._generate_performance_patches(error_analysis))
        elif error_type == ErrorType.SECURITY_ERROR:
            candidates.extend(self._generate_security_patches(error_analysis))
        elif error_type == ErrorType.VALIDATION_ERROR:
            candidates.extend(self._generate_validation_patches(error_analysis))

        return candidates
    
    def _generate_syntax_patches(self, error_analysis: ErrorAnalysis) -> List[PatchCandidate]:
        """Genera patch per errori di sintassi."""
        candidates = []
        message = error_analysis.error_message
        
        # Parentesi mancanti
        if "unexpected EOF" in message or "expected" in message:
            candidate = PatchCandidate(
                patch_type=PatchType.CODE_FIX,
                target_error_id=error_analysis.error_id,
                patch_description="Add missing parentheses or brackets",
                patch_strategy="syntax_completion",
                confidence=0.7,
                risk_level="low"
            )
            candidates.append(candidate)
        
        # Indentazione
        if "indent" in message:
            candidate = PatchCandidate(
                patch_type=PatchType.CODE_FIX,
                target_error_id=error_analysis.error_id,
                patch_description="Fix indentation",
                patch_strategy="indentation_fix",
                confidence=0.8,
                risk_level="low"
            )
            candidates.append(candidate)

        # Errori di sintassi generali
        if "invalid syntax" in message or "SyntaxError" in message:
            candidate = PatchCandidate(
                patch_type=PatchType.CODE_FIX,
                target_error_id=error_analysis.error_id,
                patch_description="Fix syntax error",
                patch_strategy="syntax_invalid",
                confidence=0.9,
                risk_level="low"
            )
            candidates.append(candidate)

        # Due punti mancanti (più specifico)
        if "invalid syntax" in message and any(keyword in error_analysis.error_context
                                              for keyword in ["if", "for", "while", "def", "class"]):
            candidate = PatchCandidate(
                patch_type=PatchType.CODE_FIX,
                target_error_id=error_analysis.error_id,
                patch_description="Add missing colon",
                patch_strategy="colon_addition",
                confidence=0.95,  # Confidence più alta per caso specifico
                risk_level="low"
            )
            candidates.append(candidate)
        
        return candidates
    
    def _generate_runtime_patches(self, error_analysis: ErrorAnalysis) -> List[PatchCandidate]:
        """FASE 2: Genera patch per errori di runtime con strategie estese."""
        candidates = []
        message = error_analysis.error_message
        pattern = error_analysis.error_pattern

        # PRIORITÀ 2: Import Error Strategies
        if pattern == "import_error" or self._is_import_error(message):
            candidates.extend(self._generate_import_patches(error_analysis))
            return candidates  # Return early per import errors

        # FASE 2: Strategie specifiche per pattern semantici
        if pattern == "undefined_variable":
            candidates.extend(self._generate_undefined_variable_patches(error_analysis))
        elif pattern == "undefined_attribute":
            candidates.extend(self._generate_undefined_attribute_patches(error_analysis))
        elif pattern == "type_mismatch":
            candidates.extend(self._generate_type_mismatch_patches(error_analysis))
        else:
            # Fallback alle strategie esistenti
            candidates.extend(self._generate_legacy_runtime_patches(error_analysis))

        # Variabile non definita (pattern esteso)
        if "is not defined" in message or "not defined" in message or "NameError" in message:
            # Pattern più flessibili per catturare variabili
            var_patterns = [
                r"name '(\w+)' is not defined",
                r"'(\w+)' is not defined",
                r"NameError.*'(\w+)'",
                r"undefined.*'(\w+)'"
            ]

            for pattern in var_patterns:
                var_match = re.search(pattern, message)
                if var_match:
                    var_name = var_match.group(1)

                    # Strategia intelligente basata sul nome della variabile
                    if "call" in var_name.lower() or var_name.endswith("_call"):
                        # Se è una funzione, rimuovila
                        candidate = PatchCandidate(
                            patch_type=PatchType.CODE_FIX,
                            target_error_id=error_analysis.error_id,
                            patch_description=f"Remove undefined function call '{var_name}'",
                            patch_strategy="remove_undefined_call",
                            confidence=0.9,
                            risk_level="low",
                            metadata={"function_name": var_name}
                        )
                        candidates.append(candidate)
                    elif var_name in ["os", "sys", "json", "time", "math", "random", "re", "datetime"]:
                        # Se è un modulo standard, aggiungilo
                        candidate = PatchCandidate(
                            patch_type=PatchType.CODE_FIX,
                            target_error_id=error_analysis.error_id,
                            patch_description=f"Add missing import for '{var_name}'",
                            patch_strategy="import_addition",
                            confidence=0.9,
                            risk_level="low",
                            metadata={"module_name": var_name}
                        )
                        candidates.append(candidate)
                    else:
                        # Altrimenti inizializza la variabile
                        candidate = PatchCandidate(
                            patch_type=PatchType.CODE_FIX,
                            target_error_id=error_analysis.error_id,
                            patch_description=f"Initialize variable '{var_name}'",
                            patch_strategy="name_error_fix",
                            confidence=0.8,
                            risk_level="low",
                            metadata={"variable_name": var_name, "undefined_name": var_name}
                        )
                        candidates.append(candidate)
                    break

        # Attributo mancante (pattern esteso)
        if "has no attribute" in message or "AttributeError" in message:
            attr_patterns = [
                r"'(\w+)' object has no attribute '(\w+)'",
                r"AttributeError.*'(\w+)'.*'(\w+)'",
                r"no attribute '(\w+)'"
            ]

            for pattern in attr_patterns:
                attr_match = re.search(pattern, message)
                if attr_match:
                    if len(attr_match.groups()) >= 2:
                        obj_type, attr_name = attr_match.groups()[:2]
                    else:
                        obj_type, attr_name = "object", attr_match.group(1)

                    candidate = PatchCandidate(
                        patch_type=PatchType.CODE_FIX,
                        target_error_id=error_analysis.error_id,
                        patch_description=f"Add safe attribute access for '{attr_name}'",
                        patch_strategy="attribute_check",
                        confidence=0.8,
                        risk_level="low"
                    )
                    candidate.metadata = {
                        "object_type": obj_type,
                        "attribute_name": attr_name,
                        "object_name": "obj"  # Default object name
                    }
                    candidates.append(candidate)
                    break

        # Import mancante (pattern esteso)
        import_patterns = [
            r"No module named '(\w+)'",
            r"ModuleNotFoundError.*'(\w+)'",
            r"ImportError.*'(\w+)'",
            r"cannot import.*'(\w+)'"
        ]

        for pattern in import_patterns:
            module_match = re.search(pattern, message)
            if module_match:
                module_name = module_match.group(1)

                candidate = PatchCandidate(
                    patch_type=PatchType.CODE_FIX,
                    target_error_id=error_analysis.error_id,
                    patch_description=f"Add import for module '{module_name}'",
                    patch_strategy="import_addition",
                    confidence=0.9,  # Alta confidence per import
                    risk_level="low"
                )
                candidate.metadata = {"module_name": module_name}
                candidates.append(candidate)
                break

        # Errori di tipo (TypeError)
        if "TypeError" in message:
            # Argomenti mancanti
            if "missing" in message and "argument" in message:
                candidate = PatchCandidate(
                    patch_type=PatchType.CODE_FIX,
                    target_error_id=error_analysis.error_id,
                    patch_description="Fix missing function arguments",
                    patch_strategy="argument_fix",
                    confidence=0.7,
                    risk_level="medium"
                )
                candidates.append(candidate)

            # Tipo non supportato
            if "unsupported" in message:
                candidate = PatchCandidate(
                    patch_type=PatchType.CODE_FIX,
                    target_error_id=error_analysis.error_id,
                    patch_description="Fix type compatibility issue",
                    patch_strategy="type_conversion",
                    confidence=0.6,
                    risk_level="medium"
                )
                candidates.append(candidate)

        # Errori di indice (IndexError)
        if "IndexError" in message or "list index out of range" in message:
            candidate = PatchCandidate(
                patch_type=PatchType.CODE_FIX,
                target_error_id=error_analysis.error_id,
                patch_description="Add bounds checking for list access",
                patch_strategy="bounds_check",
                confidence=0.8,
                risk_level="low"
            )
            candidates.append(candidate)

        # Errori di chiave (KeyError)
        if "KeyError" in message:
            key_match = re.search(r"KeyError.*'(\w+)'", message)
            key_name = key_match.group(1) if key_match else "key"

            candidate = PatchCandidate(
                patch_type=PatchType.CODE_FIX,
                target_error_id=error_analysis.error_id,
                patch_description=f"Add safe dictionary access for key '{key_name}'",
                patch_strategy="safe_dict_access",
                confidence=0.8,
                risk_level="low"
            )
            candidate.metadata = {"key_name": key_name}
            candidates.append(candidate)

        return candidates

    def _generate_legacy_runtime_patches(self, error_analysis: ErrorAnalysis) -> List[PatchCandidate]:
        """Strategie legacy per errori di runtime (per compatibilità)."""
        candidates = []
        message = error_analysis.error_message

        # Logica legacy esistente per compatibilità
        if "is not defined" in message or "NameError" in message:
            candidate = PatchCandidate(
                patch_type=PatchType.CODE_FIX,
                target_error_id=error_analysis.error_id,
                patch_description="Fix undefined variable (legacy)",
                patch_strategy="name_error_fix",
                confidence=0.6,
                risk_level="medium"
            )
            candidates.append(candidate)

        return candidates

    def _is_import_error(self, message: str) -> bool:
        """Verifica se è un errore di import."""
        message_lower = message.lower()
        return any(pattern in message_lower for pattern in [
            "no module named",
            "modulenotfounderror",
            "importerror",
            "cannot import name"
        ])

    def _generate_undefined_variable_patches(self, error_analysis: ErrorAnalysis) -> List[PatchCandidate]:
        """FASE 2: Genera patch per variabili non definite."""
        candidates = []
        message = error_analysis.error_message

        # Estrai nome variabile
        var_match = re.search(r"name '(\w+)' is not defined", message)
        var_name = var_match.group(1) if var_match else "unknown_var"

        # Strategia 1: Add name stub
        candidate = PatchCandidate(
            patch_type=PatchType.CODE_FIX,
            target_error_id=error_analysis.error_id,
            patch_description=f"Add stub for variable '{var_name}'",
            patch_strategy="add_name_stub",
            confidence=0.8,
            risk_level="low",
            metadata={"variable_name": var_name}
        )
        candidates.append(candidate)

        # Strategia 2: Wrap try/except
        candidate = PatchCandidate(
            patch_type=PatchType.CODE_FIX,
            target_error_id=error_analysis.error_id,
            patch_description=f"Wrap in try/except for '{var_name}'",
            patch_strategy="wrap_try_except",
            confidence=0.9,
            risk_level="low",
            metadata={"variable_name": var_name}
        )
        candidates.append(candidate)

        return candidates

    def _generate_undefined_attribute_patches(self, error_analysis: ErrorAnalysis) -> List[PatchCandidate]:
        """FASE 2: Genera patch per attributi non definiti."""
        candidates = []
        message = error_analysis.error_message

        # Estrai oggetto e attributo
        attr_match = re.search(r"'(\w+)' object has no attribute '(\w+)'", message)
        if attr_match:
            obj_type, attr_name = attr_match.groups()
        else:
            obj_type, attr_name = "object", "attribute"

        # Strategia: Safe attribute access
        candidate = PatchCandidate(
            patch_type=PatchType.CODE_FIX,
            target_error_id=error_analysis.error_id,
            patch_description=f"Add safe access for attribute '{attr_name}'",
            patch_strategy="safe_attribute_access",
            confidence=0.8,
            risk_level="low",
            metadata={"object_type": obj_type, "attribute_name": attr_name}
        )
        candidates.append(candidate)

        return candidates

    def _generate_type_mismatch_patches(self, error_analysis: ErrorAnalysis) -> List[PatchCandidate]:
        """FASE 2: Genera patch per errori di tipo."""
        candidates = []

        # Strategia: Type conversion
        candidate = PatchCandidate(
            patch_type=PatchType.CODE_FIX,
            target_error_id=error_analysis.error_id,
            patch_description="Add type conversion",
            patch_strategy="type_conversion",
            confidence=0.7,
            risk_level="medium"
        )
        candidates.append(candidate)

        return candidates

    def _generate_import_patches(self, error_analysis: ErrorAnalysis) -> List[PatchCandidate]:
        """Genera patch specifiche per errori di import."""
        candidates = []
        message = error_analysis.error_message

        # Estrai nome modulo dall'errore
        module_name = self._extract_module_name(message)

        # Strategia 1: Comment out import
        candidate = PatchCandidate(
            patch_type=PatchType.CODE_FIX,
            target_error_id=error_analysis.error_id,
            patch_description=f"Comment out import for '{module_name}'",
            patch_strategy="comment_out_import",
            confidence=0.8,
            risk_level="low",
            metadata={"module_name": module_name}
        )
        candidates.append(candidate)

        # Strategia 2: Add stub module
        candidate = PatchCandidate(
            patch_type=PatchType.CODE_FIX,
            target_error_id=error_analysis.error_id,
            patch_description=f"Add stub for module '{module_name}'",
            patch_strategy="add_import_stub",
            confidence=0.7,
            risk_level="medium",
            metadata={"module_name": module_name}
        )
        candidates.append(candidate)

        # Strategia 3: Try/except import
        candidate = PatchCandidate(
            patch_type=PatchType.CODE_FIX,
            target_error_id=error_analysis.error_id,
            patch_description=f"Wrap import in try/except for '{module_name}'",
            patch_strategy="try_except_import",
            confidence=0.9,
            risk_level="low",
            metadata={"module_name": module_name}
        )
        candidates.append(candidate)

        return candidates

    def _extract_module_name(self, error_message: str) -> str:
        """Estrae il nome del modulo dall'errore di import."""
        # Pattern per "No module named 'xyz'"
        import_pattern = r"No module named ['\"]([^'\"]+)['\"]"
        match = re.search(import_pattern, error_message, re.IGNORECASE)
        if match:
            return match.group(1)

        # Pattern per "cannot import name 'xyz'"
        name_pattern = r"cannot import name ['\"]([^'\"]+)['\"]"
        match = re.search(name_pattern, error_message, re.IGNORECASE)
        if match:
            return match.group(1)

        # Fallback: cerca parole dopo "module"
        words = error_message.split()
        for i, word in enumerate(words):
            if word.lower() == "module" and i + 2 < len(words):
                return words[i + 2].strip("'\"")

        return "unknown_module"

    def _generate_logic_patches(self, error_analysis: ErrorAnalysis) -> List[PatchCandidate]:
        """Genera patch per errori logici."""
        candidates = []
        
        if "contradiction" in error_analysis.error_message.lower():
            candidate = PatchCandidate(
                patch_type=PatchType.LOGIC_FIX,
                target_error_id=error_analysis.error_id,
                patch_description="Resolve logical contradiction",
                patch_strategy="contradiction_resolution",
                confidence=0.5,
                risk_level="high"
            )
            candidates.append(candidate)
        
        if "low quality reasoning" in error_analysis.error_message.lower():
            candidate = PatchCandidate(
                patch_type=PatchType.ALGORITHM_REPLACEMENT,
                target_error_id=error_analysis.error_id,
                patch_description="Improve reasoning quality",
                patch_strategy="reasoning_enhancement",
                confidence=0.4,
                risk_level="medium"
            )
            candidates.append(candidate)
        
        return candidates
    
    def _generate_performance_patches(self, error_analysis: ErrorAnalysis) -> List[PatchCandidate]:
        """Genera patch per errori di performance."""
        candidates = []
        
        if "timeout" in error_analysis.error_message.lower():
            # Patch: ottimizzazione algoritmo
            candidate = PatchCandidate(
                patch_type=PatchType.PERFORMANCE_OPTIMIZATION,
                target_error_id=error_analysis.error_id,
                patch_description="Optimize algorithm for better performance",
                patch_strategy="algorithm_optimization",
                confidence=0.6,
                risk_level="medium"
            )
            candidates.append(candidate)
            
            # Patch: aumento timeout
            candidate = PatchCandidate(
                patch_type=PatchType.PARAMETER_ADJUSTMENT,
                target_error_id=error_analysis.error_id,
                patch_description="Increase timeout limit",
                patch_strategy="timeout_adjustment",
                confidence=0.8,
                risk_level="low"
            )
            candidates.append(candidate)
        
        return candidates
    
    def _generate_security_patches(self, error_analysis: ErrorAnalysis) -> List[PatchCandidate]:
        """Genera patch per errori di sicurezza."""
        candidates = []
        
        candidate = PatchCandidate(
            patch_type=PatchType.SECURITY_HARDENING,
            target_error_id=error_analysis.error_id,
            patch_description="Apply security hardening",
            patch_strategy="security_enhancement",
            confidence=0.9,
            risk_level="low"
        )
        candidates.append(candidate)
        
        return candidates

    def _generate_validation_patches(self, error_analysis: ErrorAnalysis) -> List[PatchCandidate]:
        """Genera patch per errori di validazione."""
        candidates = []
        message = error_analysis.error_message.lower()
        pattern = error_analysis.error_pattern

        # Patch per missing premises
        if "missing" in message and "premise" in message:
            candidate = PatchCandidate(
                patch_type=PatchType.LOGIC_FIX,
                target_error_id=error_analysis.error_id,
                patch_description="Add missing logical premises",
                patch_strategy="premise_addition",
                confidence=0.6,
                risk_level="medium"
            )
            candidates.append(candidate)

        # Patch per logical dependency errors
        if pattern == "logical_dependency_error":
            candidate = PatchCandidate(
                patch_type=PatchType.LOGIC_FIX,
                target_error_id=error_analysis.error_id,
                patch_description="Resolve logical dependency issue",
                patch_strategy="dependency_resolution",
                confidence=0.7,
                risk_level="medium"
            )
            candidates.append(candidate)

        # Patch per invalid references
        if "reference" in message and "invalid" in message:
            candidate = PatchCandidate(
                patch_type=PatchType.CODE_FIX,
                target_error_id=error_analysis.error_id,
                patch_description="Fix invalid reference",
                patch_strategy="reference_correction",
                confidence=0.8,
                risk_level="low"
            )
            candidates.append(candidate)

        # Patch per circular inference
        if "circular" in message:
            candidate = PatchCandidate(
                patch_type=PatchType.ALGORITHM_REPLACEMENT,
                target_error_id=error_analysis.error_id,
                patch_description="Break circular inference",
                patch_strategy="circularity_breaking",
                confidence=0.5,
                risk_level="high"
            )
            candidates.append(candidate)

        return candidates

    def _generate_learned_patches(self, error_analysis: ErrorAnalysis) -> List[PatchCandidate]:
        """Genera patch basate su pattern appresi."""
        candidates = []
        
        # Cerca pattern simili nei pattern appresi
        for pattern in self.learned_patterns:
            if self._pattern_matches(pattern, error_analysis):
                candidate = self._create_patch_from_learned_pattern(
                    error_analysis, pattern
                )
                if candidate:
                    candidates.append(candidate)
        
        return candidates
    
    def _create_patch_from_template(self, error_analysis: ErrorAnalysis, 
                                   template: Dict[str, Any], 
                                   strategy: Dict[str, Any]) -> Optional[PatchCandidate]:
        """Crea patch da template."""
        candidate = PatchCandidate(
            patch_type=PatchType(template.get('type', 'code_fix')),
            target_error_id=error_analysis.error_id,
            patch_description=template.get('description', ''),
            patch_strategy=template.get('strategy', ''),
            confidence=template.get('confidence', 0.5),
            risk_level=template.get('risk_level', 'medium')
        )
        
        # Personalizza patch basata su contesto errore
        candidate = self._customize_patch(candidate, error_analysis)
        
        return candidate
    
    def _create_patch_from_learned_pattern(self, error_analysis: ErrorAnalysis,
                                          pattern: LearningPattern) -> Optional[PatchCandidate]:
        """Crea patch da pattern appreso."""
        candidate = PatchCandidate(
            patch_type=PatchType.CODE_FIX,
            target_error_id=error_analysis.error_id,
            patch_description=f"Apply learned pattern: {pattern.pattern_name}",
            patch_strategy="learned_pattern",
            confidence=pattern.effectiveness_score,
            risk_level="medium"
        )
        
        candidate.metadata = {
            "learned_pattern_id": pattern.pattern_id,
            "pattern_effectiveness": pattern.effectiveness_score
        }
        
        return candidate
    
    def _customize_patch(self, candidate: PatchCandidate, 
                        error_analysis: ErrorAnalysis) -> PatchCandidate:
        """Personalizza patch basata su contesto errore."""
        # Aggiusta confidence basata su severità errore
        if error_analysis.severity == "critical":
            candidate.confidence *= 1.2  # Aumenta confidence per errori critici
        elif error_analysis.severity == "low":
            candidate.confidence *= 0.8  # Diminuisci per errori minori
        
        # Aggiusta risk level
        if error_analysis.impact_score > 0.8:
            if candidate.risk_level == "low":
                candidate.risk_level = "medium"
            elif candidate.risk_level == "medium":
                candidate.risk_level = "high"
        
        # Clamp confidence
        candidate.confidence = max(0.0, min(1.0, candidate.confidence))
        
        return candidate
    
    def _filter_candidates(self, candidates: List[PatchCandidate]) -> List[PatchCandidate]:
        """Filtra candidati patch."""
        filtered = []
        
        for candidate in candidates:
            # Filtra per confidence minima
            if candidate.confidence < self.config.min_confidence_threshold:
                continue
            
            # Filtra per risk level massimo
            risk_levels = ["low", "medium", "high", "critical"]
            max_risk_index = risk_levels.index(self.config.max_risk_level)
            candidate_risk_index = risk_levels.index(candidate.risk_level)
            
            if candidate_risk_index > max_risk_index:
                continue
            
            filtered.append(candidate)
        
        return filtered
    
    def _sort_candidates(self, candidates: List[PatchCandidate]) -> List[PatchCandidate]:
        """Ordina candidati per qualità."""
        def patch_score(candidate: PatchCandidate) -> float:
            # Score basato su confidence e risk
            risk_penalty = {
                "low": 0.0,
                "medium": 0.1,
                "high": 0.3,
                "critical": 0.5
            }
            
            penalty = risk_penalty.get(candidate.risk_level, 0.2)
            return candidate.confidence - penalty
        
        return sorted(candidates, key=patch_score, reverse=True)
    
    def _pattern_matches(self, pattern: LearningPattern, 
                        error_analysis: ErrorAnalysis) -> bool:
        """Verifica se un pattern appreso corrisponde all'errore."""
        # Matching semplice basato su pattern dell'errore
        return pattern.error_pattern == error_analysis.error_pattern
    
    def _load_patch_strategies(self) -> Dict[str, Dict[str, Any]]:
        """Carica strategie di patch."""
        return {
            "undefined_variable": {
                "patches": [
                    {
                        "type": "code_fix",
                        "description": "Initialize variable with default value",
                        "strategy": "variable_init",
                        "confidence": 0.7,
                        "risk_level": "medium"
                    }
                ]
            },
            "missing_attribute": {
                "patches": [
                    {
                        "type": "code_fix", 
                        "description": "Add attribute check",
                        "strategy": "attribute_guard",
                        "confidence": 0.8,
                        "risk_level": "low"
                    }
                ]
            },
            "syntax_invalid": {
                "patches": [
                    {
                        "type": "code_fix",
                        "description": "Fix syntax error",
                        "strategy": "syntax_repair",
                        "confidence": 0.9,
                        "risk_level": "low"
                    }
                ]
            },
            "logical_dependency_error": {
                "patches": [
                    {
                        "type": "logic_fix",
                        "description": "Resolve logical dependency issue",
                        "strategy": "dependency_resolution",
                        "confidence": 0.7,
                        "risk_level": "medium"
                    },
                    {
                        "type": "logic_fix",
                        "description": "Add missing premises",
                        "strategy": "premise_addition",
                        "confidence": 0.6,
                        "risk_level": "medium"
                    }
                ]
            }
        }
    
    def add_learned_pattern(self, pattern: LearningPattern):
        """Aggiunge pattern appreso."""
        self.learned_patterns.append(pattern)
    
    def get_generation_statistics(self) -> Dict[str, Any]:
        """Ottiene statistiche di generazione."""
        return {
            "strategies_available": len(self.patch_strategies),
            "learned_patterns": len(self.learned_patterns),
            "max_candidates": self.config.max_patch_candidates,
            "min_confidence": self.config.min_confidence_threshold
        }
