"""
FASE 4.1 - AST Round-Trip Validator per NEUROGLYPH

Validazione completa del round-trip AST:
- Parse → Compress → Decompress → Parse
- AST equivalenza verification
- Performance benchmarking
- Real-world corpus testing
"""

import ast
import time
import hashlib
import gzip
import brotli
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
import json


@dataclass
class RoundTripResult:
    """Risultato di un test round-trip."""
    file_path: str
    original_size: int
    compressed_size: int
    compression_ratio: float
    round_trip_success: bool
    ast_equivalent: bool
    parse_time_ms: float
    compress_time_ms: float
    decompress_time_ms: float
    total_time_ms: float
    error_message: Optional[str] = None
    ast_diff: Optional[str] = None


@dataclass
class ValidationMetrics:
    """Metriche aggregate di validazione."""
    total_files: int = 0
    successful_round_trips: int = 0
    ast_equivalent_count: int = 0
    total_original_size: int = 0
    total_compressed_size: int = 0
    avg_compression_ratio: float = 0.0
    avg_round_trip_time_ms: float = 0.0
    round_trip_success_rate: float = 0.0
    ast_equivalence_rate: float = 0.0
    
    def update(self, result: RoundTripResult):
        """Aggiorna metriche con nuovo risultato."""
        self.total_files += 1
        self.total_original_size += result.original_size
        self.total_compressed_size += result.compressed_size
        
        if result.round_trip_success:
            self.successful_round_trips += 1
        
        if result.ast_equivalent:
            self.ast_equivalent_count += 1
        
        # Ricalcola metriche aggregate
        self.round_trip_success_rate = (self.successful_round_trips / self.total_files) * 100
        self.ast_equivalence_rate = (self.ast_equivalent_count / self.total_files) * 100
        self.avg_compression_ratio = (self.total_compressed_size / self.total_original_size) * 100
    
    def get_summary(self) -> Dict[str, Any]:
        """Ottieni summary delle metriche."""
        return {
            "total_files": self.total_files,
            "round_trip_success_rate": round(self.round_trip_success_rate, 2),
            "ast_equivalence_rate": round(self.ast_equivalence_rate, 2),
            "avg_compression_ratio": round(self.avg_compression_ratio, 2),
            "total_size_mb": round(self.total_original_size / 1024 / 1024, 2),
            "compressed_size_mb": round(self.total_compressed_size / 1024 / 1024, 2),
            "space_saved_mb": round((self.total_original_size - self.total_compressed_size) / 1024 / 1024, 2)
        }


class ASTRoundTripValidator:
    """
    FASE 4.1: Validator per AST round-trip testing.
    
    Testa il flusso completo:
    Python Code → AST → NEUROGLYPH Symbols → AST → Python Code
    """
    
    def __init__(self):
        self.metrics = ValidationMetrics()
        self.failed_files = []
        self.results = []
        
        # Lazy import per evitare dipendenze circolari
        self._encoder = None
        self._decoder = None
    
    def _get_encoder(self):
        """Lazy loading dell'encoder."""
        if self._encoder is None:
            try:
                from ..symbolic.encoder import NeuroglyphEncoder
                self._encoder = NeuroglyphEncoder()
            except ImportError:
                print("⚠️ NeuroglyphEncoder not available, using mock")
                self._encoder = MockEncoder()
        return self._encoder
    
    def _get_decoder(self):
        """Lazy loading del decoder."""
        if self._decoder is None:
            try:
                from ..symbolic.decoder import NeuroglyphDecoder
                self._decoder = NeuroglyphDecoder()
            except ImportError:
                print("⚠️ NeuroglyphDecoder not available, using mock")
                self._decoder = MockDecoder()
        return self._decoder
    
    def validate_file(self, file_path: Path) -> RoundTripResult:
        """
        Valida un singolo file Python.
        
        Args:
            file_path: Path al file Python
            
        Returns:
            Risultato della validazione
        """
        start_time = time.perf_counter()
        
        try:
            # Leggi file originale
            with open(file_path, 'r', encoding='utf-8') as f:
                original_code = f.read()
            
            original_size = len(original_code.encode('utf-8'))
            
            # Step 1: Parse AST originale
            parse_start = time.perf_counter()
            try:
                original_ast = ast.parse(original_code)
                original_ast_dump = ast.dump(original_ast, indent=2)
            except SyntaxError as e:
                return RoundTripResult(
                    file_path=str(file_path),
                    original_size=original_size,
                    compressed_size=0,
                    compression_ratio=0.0,
                    round_trip_success=False,
                    ast_equivalent=False,
                    parse_time_ms=0,
                    compress_time_ms=0,
                    decompress_time_ms=0,
                    total_time_ms=0,
                    error_message=f"Original parse error: {e}"
                )
            parse_time = (time.perf_counter() - parse_start) * 1000
            
            # Step 2: Compress con NEUROGLYPH
            compress_start = time.perf_counter()
            encoder = self._get_encoder()
            compressed_symbols = encoder.encode(original_code)
            compressed_size = len(str(compressed_symbols).encode('utf-8'))
            compress_time = (time.perf_counter() - compress_start) * 1000
            
            # Step 3: Decompress con NEUROGLYPH
            decompress_start = time.perf_counter()
            decoder = self._get_decoder()
            reconstructed_code = decoder.decode(compressed_symbols)
            decompress_time = (time.perf_counter() - decompress_start) * 1000
            
            # Step 4: Parse AST ricostruito
            try:
                reconstructed_ast = ast.parse(reconstructed_code)
                reconstructed_ast_dump = ast.dump(reconstructed_ast, indent=2)
                round_trip_success = True
            except SyntaxError as e:
                return RoundTripResult(
                    file_path=str(file_path),
                    original_size=original_size,
                    compressed_size=compressed_size,
                    compression_ratio=(compressed_size / original_size) * 100,
                    round_trip_success=False,
                    ast_equivalent=False,
                    parse_time_ms=parse_time,
                    compress_time_ms=compress_time,
                    decompress_time_ms=decompress_time,
                    total_time_ms=0,
                    error_message=f"Reconstructed parse error: {e}"
                )
            
            # Step 5: Confronta AST
            ast_equivalent = original_ast_dump == reconstructed_ast_dump
            ast_diff = None if ast_equivalent else self._compute_ast_diff(
                original_ast_dump, reconstructed_ast_dump
            )
            
            total_time = (time.perf_counter() - start_time) * 1000
            compression_ratio = (compressed_size / original_size) * 100
            
            return RoundTripResult(
                file_path=str(file_path),
                original_size=original_size,
                compressed_size=compressed_size,
                compression_ratio=compression_ratio,
                round_trip_success=round_trip_success,
                ast_equivalent=ast_equivalent,
                parse_time_ms=parse_time,
                compress_time_ms=compress_time,
                decompress_time_ms=decompress_time,
                total_time_ms=total_time,
                ast_diff=ast_diff
            )
            
        except Exception as e:
            return RoundTripResult(
                file_path=str(file_path),
                original_size=0,
                compressed_size=0,
                compression_ratio=0.0,
                round_trip_success=False,
                ast_equivalent=False,
                parse_time_ms=0,
                compress_time_ms=0,
                decompress_time_ms=0,
                total_time_ms=0,
                error_message=f"Validation error: {e}"
            )
    
    def _compute_ast_diff(self, original: str, reconstructed: str) -> str:
        """Computa differenze tra AST."""
        # Semplice diff per ora - può essere migliorato
        original_lines = original.split('\n')
        reconstructed_lines = reconstructed.split('\n')
        
        diff_lines = []
        max_lines = max(len(original_lines), len(reconstructed_lines))
        
        for i in range(max_lines):
            orig_line = original_lines[i] if i < len(original_lines) else ""
            recon_line = reconstructed_lines[i] if i < len(reconstructed_lines) else ""
            
            if orig_line != recon_line:
                diff_lines.append(f"Line {i+1}:")
                diff_lines.append(f"  Original:      {orig_line}")
                diff_lines.append(f"  Reconstructed: {recon_line}")
        
        return '\n'.join(diff_lines[:20])  # Limita a prime 20 differenze
    
    def validate_corpus(self, corpus_path: Path, max_files: Optional[int] = None) -> ValidationMetrics:
        """
        Valida un corpus completo di file Python.
        
        Args:
            corpus_path: Path alla directory del corpus
            max_files: Numero massimo di file da testare
            
        Returns:
            Metriche aggregate
        """
        print(f"🚀 Starting AST Round-Trip Validation on corpus: {corpus_path}")
        
        # Trova tutti i file Python
        python_files = list(corpus_path.rglob("*.py"))
        
        if max_files:
            python_files = python_files[:max_files]
        
        print(f"📁 Found {len(python_files)} Python files to validate")
        
        # Valida ogni file
        for i, file_path in enumerate(python_files):
            if i % 10 == 0:
                print(f"   Progress: {i}/{len(python_files)} files processed...")
            
            result = self.validate_file(file_path)
            self.results.append(result)
            self.metrics.update(result)
            
            if not result.round_trip_success or not result.ast_equivalent:
                self.failed_files.append(result)
        
        print(f"✅ Validation completed: {len(python_files)} files processed")
        return self.metrics
    
    def generate_report(self, output_path: Path):
        """Genera report dettagliato."""
        report = {
            "summary": self.metrics.get_summary(),
            "detailed_results": [asdict(result) for result in self.results],
            "failed_files": [asdict(result) for result in self.failed_files],
            "timestamp": time.time()
        }
        
        with open(output_path, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"📊 Report saved to: {output_path}")
    
    def benchmark_compression(self, file_path: Path) -> Dict[str, Any]:
        """Benchmark compression vs Gzip/Brotli."""
        with open(file_path, 'rb') as f:
            original_data = f.read()
        
        original_size = len(original_data)
        
        # Gzip compression
        gzip_compressed = gzip.compress(original_data)
        gzip_ratio = (len(gzip_compressed) / original_size) * 100
        
        # Brotli compression
        brotli_compressed = brotli.compress(original_data)
        brotli_ratio = (len(brotli_compressed) / original_size) * 100
        
        # NEUROGLYPH compression
        result = self.validate_file(file_path)
        neuroglyph_ratio = result.compression_ratio
        
        return {
            "file": str(file_path),
            "original_size": original_size,
            "gzip_ratio": round(gzip_ratio, 2),
            "brotli_ratio": round(brotli_ratio, 2),
            "neuroglyph_ratio": round(neuroglyph_ratio, 2),
            "neuroglyph_maintains_ast": result.ast_equivalent
        }


class MockEncoder:
    """Mock encoder per testing senza dipendenze."""
    def encode(self, code: str) -> str:
        # Semplice mock che simula compressione
        return f"<MOCK_COMPRESSED>{hashlib.md5(code.encode()).hexdigest()}</MOCK_COMPRESSED>"


class MockDecoder:
    """Mock decoder per testing senza dipendenze."""
    def decode(self, symbols: str) -> str:
        # Mock che ritorna codice semplice per test
        return "# Mock decoded code\nprint('Hello, World!')\n"
