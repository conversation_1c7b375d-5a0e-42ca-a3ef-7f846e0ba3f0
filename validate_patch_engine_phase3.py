#!/usr/bin/env python3
"""
NEUROGLYPH Patch Engine Validation - Fase 3
Test completo delle nuove strategie implementate
"""

import time
from typing import List, Dict, Any

def create_test_cases() -> List[Dict[str, Any]]:
    """Crea test cases per validazione Fase 3."""
    from neuroglyph.cognitive.validation_structures import ValidationErrorType, ValidationLevel
    
    return [
        # Import Errors
        {
            "name": "Import Error - Module Not Found",
            "code": "import nonexistent_module\nresult = nonexistent_module.func()",
            "error_message": "ModuleNotFoundError: No module named 'nonexistent_module'",
            "error_type": ValidationErrorType.MISSING_PREMISES,
            "error_level": ValidationLevel.SEMANTIC,
            "expected_strategy": "try_except_import"
        },
        {
            "name": "Import Error - Cannot Import Name",
            "code": "from os import nonexistent_function\nresult = nonexistent_function()",
            "error_message": "ImportError: cannot import name 'nonexistent_function'",
            "error_type": ValidationErrorType.MISSING_PREMISES,
            "error_level": ValidationLevel.SEMANTIC,
            "expected_strategy": "try_except_import"
        },
        
        # Syntax Errors
        {
            "name": "Syntax Error - Missing Colon",
            "code": "if True\n    print('hello')",
            "error_message": "SyntaxError: invalid syntax",
            "error_type": ValidationErrorType.AST_PARSE_ERROR,
            "error_level": ValidationLevel.SYNTAX,
            "expected_strategy": "fix_missing_colon"
        },
        {
            "name": "Syntax Error - Unclosed Parenthesis",
            "code": "print('hello'\nprint('world')",
            "error_message": "SyntaxError: unexpected EOF while parsing",
            "error_type": ValidationErrorType.AST_PARSE_ERROR,
            "error_level": ValidationLevel.SYNTAX,
            "expected_strategy": "fix_close_paren"
        },
        
        # Semantic Errors
        {
            "name": "Undefined Variable",
            "code": "print(undefined_var)\nresult = undefined_var + 5",
            "error_message": "NameError: name 'undefined_var' is not defined",
            "error_type": ValidationErrorType.MISSING_PREMISES,
            "error_level": ValidationLevel.SEMANTIC,
            "expected_strategy": "add_name_stub"
        },
        {
            "name": "Undefined Attribute",
            "code": "obj = object()\nresult = obj.nonexistent_attr",
            "error_message": "AttributeError: 'object' object has no attribute 'nonexistent_attr'",
            "error_type": ValidationErrorType.MISSING_PREMISES,
            "error_level": ValidationLevel.SEMANTIC,
            "expected_strategy": "safe_attribute_access"
        },
        {
            "name": "Type Mismatch",
            "code": "result = 'hello' + 5",
            "error_message": "TypeError: can't concatenate str and int",
            "error_type": ValidationErrorType.TYPE_MISMATCH,
            "error_level": ValidationLevel.SEMANTIC,
            "expected_strategy": "type_conversion"
        }
    ]

def validate_single_test(test_case: Dict[str, Any]) -> Dict[str, Any]:
    """Valida un singolo test case."""
    from neuroglyph.cognitive.adaptive_patcher import NGAdaptivePatcher
    from neuroglyph.cognitive.validation_structures import ValidationResult, ValidationError, ValidationSeverity
    
    print(f"\n🧪 Testing: {test_case['name']}")
    print(f"Expected strategy: {test_case['expected_strategy']}")
    
    # Crea nuovo patcher per ogni test
    patcher = NGAdaptivePatcher()
    
    # Crea ValidationResult
    result = ValidationResult()
    val_error = ValidationError(
        error_type=test_case["error_type"],
        severity=ValidationSeverity.ERROR,
        level=test_case["error_level"],
        message=test_case["error_message"],
        line_number=1
    )
    val_error.context = {'code': test_case["code"]}
    result.add_error(val_error)
    result.finalize()
    
    # Applica patch
    start_time = time.time()
    patch_results = patcher.patch_validation_errors(result)
    duration = time.time() - start_time
    
    # Analizza risultati
    test_result = {
        "name": test_case["name"],
        "expected_strategy": test_case["expected_strategy"],
        "success": False,
        "patch_applied": False,
        "correct_strategy": False,
        "compilation_success": False,
        "validation_passed": False,
        "duration_ms": duration * 1000,
        "error_message": "",
        "actual_strategy": "",
        "patched_code": ""
    }
    
    if patch_results:
        patch_result = patch_results[0]
        test_result["patch_applied"] = True
        test_result["success"] = patch_result.success
        test_result["validation_passed"] = patch_result.validation_passed
        test_result["error_message"] = patch_result.error_message or ""
        
        # Estrai strategia applicata e codice patchato
        if hasattr(patch_result, 'metadata') and patch_result.metadata:
            test_result["patched_code"] = patch_result.metadata.get('patched_code', '')
            # La strategia è nel candidate che ha generato la patch
            # Per ora usiamo un'euristica basata sul codice patchato
            if 'try:' in test_result["patched_code"] and 'import' in test_result["patched_code"]:
                test_result["actual_strategy"] = "try_except_import"
            elif ':' in test_result["patched_code"] and test_case["code"].count(':') < test_result["patched_code"].count(':'):
                test_result["actual_strategy"] = "fix_missing_colon"
            elif test_result["patched_code"].count(')') > test_case["code"].count(')'):
                test_result["actual_strategy"] = "fix_close_paren"
            elif 'None  # Auto-generated' in test_result["patched_code"]:
                test_result["actual_strategy"] = "add_name_stub"
            else:
                test_result["actual_strategy"] = "unknown"
        
        # Verifica se la strategia è corretta
        test_result["correct_strategy"] = (test_result["actual_strategy"] == test_case["expected_strategy"])
        
        # Verifica compilazione
        try:
            compile(test_result["patched_code"], '<string>', 'exec')
            test_result["compilation_success"] = True
        except:
            test_result["compilation_success"] = False
    
    # Stampa risultato
    status = "✅" if test_result["success"] and test_result["correct_strategy"] else "❌"
    print(f"{status} Result: {test_result['success']}")
    print(f"   Strategy: {test_result['actual_strategy']} (expected: {test_result['expected_strategy']})")
    print(f"   Compilation: {test_result['compilation_success']}")
    print(f"   Duration: {test_result['duration_ms']:.1f}ms")
    
    return test_result

def main():
    """Esegue validazione completa Fase 3."""
    print("🚀 NEUROGLYPH Patch Engine Validation - Fase 3")
    print("=" * 60)
    
    test_cases = create_test_cases()
    results = []
    
    for test_case in test_cases:
        try:
            result = validate_single_test(test_case)
            results.append(result)
        except Exception as e:
            print(f"❌ Error in test {test_case['name']}: {e}")
            results.append({
                "name": test_case["name"],
                "success": False,
                "error_message": str(e)
            })
    
    # Calcola metriche finali
    print("\n" + "=" * 60)
    print("📊 RISULTATI FINALI")
    print("=" * 60)
    
    total_tests = len(results)
    successful_patches = sum(1 for r in results if r.get("success", False))
    correct_strategies = sum(1 for r in results if r.get("correct_strategy", False))
    compilation_success = sum(1 for r in results if r.get("compilation_success", False))
    
    patch_success_rate = (successful_patches / total_tests) * 100 if total_tests > 0 else 0
    strategy_accuracy = (correct_strategies / total_tests) * 100 if total_tests > 0 else 0
    compilation_rate = (compilation_success / total_tests) * 100 if total_tests > 0 else 0
    
    print(f"Total Tests: {total_tests}")
    print(f"Patch Success Rate: {patch_success_rate:.1f}% ({successful_patches}/{total_tests})")
    print(f"Strategy Accuracy: {strategy_accuracy:.1f}% ({correct_strategies}/{total_tests})")
    print(f"Compilation Success Rate: {compilation_rate:.1f}% ({compilation_success}/{total_tests})")
    
    # Target: 70% patch success rate
    target_success_rate = 70.0
    if patch_success_rate >= target_success_rate:
        print(f"\n🎉 TARGET RAGGIUNTO! Success rate {patch_success_rate:.1f}% >= {target_success_rate}%")
    else:
        print(f"\n🔄 Target non raggiunto. Success rate {patch_success_rate:.1f}% < {target_success_rate}%")
        print("   Aree di miglioramento:")
        for result in results:
            if not result.get("success", False):
                print(f"   - {result['name']}: {result.get('error_message', 'Unknown error')}")
    
    print("\n" + "=" * 60)
    
    return results

if __name__ == "__main__":
    main()
